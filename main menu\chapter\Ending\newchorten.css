/* Base Styles */
body {
    margin: 0;
    overflow: hidden;
    font-family: 'Press Start 2P', cursive;
    background-color: #000;
}

/* Black screen overlay for smooth transition */
#black-screen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 10000;
    opacity: 1;
    transition: opacity 2s ease-out;
    pointer-events: none;
}

#black-screen-overlay.fade-out {
    opacity: 0;
}

canvas {
    display: block;
    margin: 0 auto;
}

/* Message Boxes */
.message-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #FFD700;
    padding: 2rem;
    border-radius: 10px;
    border: 4px solid #DAA520;
    text-align: center;
    max-width: 80%;
    z-index: 100;
    box-shadow: 0 0 20px rgba(218, 165, 32, 0.7);
}

/* Message boxes */
#message {
    display: none; /* Hidden by default - shows after girl reaches chorten */
}

#congrats-message {
    display: none; /* Hidden by default */
}

.hidden {
    display: none !important;
}

/* Message styling - exact copy from chorten page with fade-in */
#message {
    position: absolute;
    top: 20%;
    left: 50%;
    background-image: url('./Layer_1.png');
    height: 210px;
    width: 450px;
    transform: translate(-50%, -50%);
    font-family: 'Press Start 2P', cursive;
    color: black;
    text-align: center;
    font-size: 15px;
    display: none;
    z-index: 90;
    max-width: 80%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

#message.fade-in {
    opacity: 1;
}

/* Text positioning within scroll */
#message p {
    margin: 0;
    padding: 0 20px;
    line-height: 1.2;
}

#congrats-message {
    position: fixed;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #FFD700;
    padding: 30px 40px;
    border-radius: 15px;
    border: 4px solid #DAA520;
    text-align: center;
    z-index: 100;
    box-shadow: 0 0 30px rgba(218, 165, 32, 0.8), inset 0 0 20px rgba(255, 215, 0, 0.1);
    backdrop-filter: blur(10px);
    max-width: 500px;
    min-width: 400px;
}

#congrats-message h2 {
    font-size: 1.5rem;
    color: #FFD700;
    margin-bottom: 15px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9), 0 0 20px #FFD700;
    }
    to {
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9), 0 0 30px #FFD700, 0 0 40px #FFD700;
    }
}

/* Button styling - positioned to extend beyond scroll like chorten page */
#close-message {
    margin-top: 20px;
    padding: 10px 25px;
    background-color: lightgreen;
    color: #000;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.3s;
    position: relative;
    z-index: 91;
}

#close-message:hover {
    background-color: #ffd700;
    transform: scale(1.05);
}

.game-button {
    margin-top: 1.5rem;
    padding: 12px 25px;
    background-color: #DAA520;
    color: #000;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-family: 'Press Start 2P', cursive;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 120px;
}

.game-button:hover {
    background-color: #FFD700;
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(255, 215, 0, 0.4),
        0 8px 16px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Glitter Particles */
#glitter {
    position: fixed;
    top: 0; left: 0; width: 100vw; height: 100vh;
    pointer-events: none;
    z-index: 9999;
    background: transparent;
}

.glitter-dot {
    position: absolute;
    width: 8px; height: 8px;
    border-radius: 50%;
    background: radial-gradient(circle, #fff, #ffd700, transparent 70%);
    opacity: 0.8;
    animation: glitter-pop 1s linear forwards;
}

@keyframes glitter-pop {
    0% { transform: scale(0.5); opacity: 1; }
    80% { transform: scale(1.5); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

.glitter-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: gold;
    border-radius: 50%;
    pointer-events: none;
    z-index: 10;
    opacity: 0;
    animation: glitter-fall 2s forwards;
}

@keyframes glitter-fall {
    0% {
        transform: translate(0, 0) scale(0.5);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    100% {
        transform: translate(var(--tx), var(--ty)) scale(1);
        opacity: 0;
    }
}
